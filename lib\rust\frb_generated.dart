// This file is automatically generated, so please do not edit it.
// @generated by `flutter_rust_bridge`@ 2.10.0.

// ignore_for_file: unused_import, unused_element, unnecessary_import, duplicate_ignore, invalid_use_of_internal_member, annotate_overrides, non_constant_identifier_names, curly_braces_in_flow_control_structures, prefer_const_literals_to_create_immutables, unused_field

import 'api/api.dart';
import 'api/gobang.dart';
import 'dart:async';
import 'dart:convert';
import 'frb_generated.dart';
import 'frb_generated.io.dart' if (dart.library.js_interop) 'frb_generated.web.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

/// Main entrypoint of the Rust API
class RustLib extends BaseEntrypoint<RustLibApi, RustLibApiImpl, RustLibWire> {
  @internal
  static final instance = RustLib._();

  RustLib._();

  /// Initialize flutter_rust_bridge
  static Future<void> init({RustLibApi? api, BaseHandler? handler, ExternalLibrary? externalLibrary}) async {
    await instance.initImpl(api: api, handler: handler, externalLibrary: externalLibrary);
  }

  /// Initialize flutter_rust_bridge in mock mode.
  /// No libraries for FFI are loaded.
  static void initMock({required RustLibApi api}) {
    instance.initMockImpl(api: api);
  }

  /// Dispose flutter_rust_bridge
  ///
  /// The call to this function is optional, since flutter_rust_bridge (and everything else)
  /// is automatically disposed when the app stops.
  static void dispose() => instance.disposeImpl();

  @override
  ApiImplConstructor<RustLibApiImpl, RustLibWire> get apiImplConstructor => RustLibApiImpl.new;

  @override
  WireConstructor<RustLibWire> get wireConstructor => RustLibWire.fromExternalLibrary;

  @override
  Future<void> executeRustInitializers() async {
    await api.crateApiApiInitApp();
  }

  @override
  ExternalLibraryLoaderConfig get defaultExternalLibraryLoaderConfig => kDefaultExternalLibraryLoaderConfig;

  @override
  String get codegenVersion => '2.10.0';

  @override
  int get rustContentHash => -237481449;

  static const kDefaultExternalLibraryLoaderConfig = ExternalLibraryLoaderConfig(
    stem: 'rust_lib_test_gobang11',
    ioDirectory: 'rust/target/release/',
    webPrefix: 'pkg/',
  );
}

abstract class RustLibApi extends BaseApi {
  Future<GobangGame> crateApiGobangGobangGameDefault();

  Future<int> crateApiGobangGobangGameGetBoardSize({required GobangGame that});

  Future<PieceType> crateApiGobangGobangGameGetCurrentPlayer({required GobangGame that});

  Future<GameMode> crateApiGobangGobangGameGetGameMode({required GobangGame that});

  Future<GameState> crateApiGobangGobangGameGetGameState({required GobangGame that});

  Future<int> crateApiGobangGobangGameGetMoveCount({required GobangGame that});

  Future<PieceType> crateApiGobangGobangGameGetPiece({required GobangGame that, required int row, required int col});

  Future<bool> crateApiGobangGobangGameMakeMove({required GobangGame that, required int row, required int col});

  Future<GobangGame> crateApiGobangGobangGameNew({required BigInt boardSize, required GameMode gameMode});

  Future<void> crateApiGobangGobangGameReset({required GobangGame that});

  Future<bool> crateApiGobangGobangGameUndoMove({required GobangGame that});

  GobangGame crateApiGobangCreateGobangGame();

  int crateApiGobangGameGetBoardSize({required GobangGame game});

  PieceType crateApiGobangGameGetCurrentPlayer({required GobangGame game});

  int crateApiGobangGameGetMoveCount({required GobangGame game});

  PieceType crateApiGobangGameGetPiece({required GobangGame game, required int row, required int col});

  GameState crateApiGobangGameGetState({required GobangGame game});

  bool crateApiGobangGameMakeMove({required GobangGame game, required int row, required int col});

  void crateApiGobangGameReset({required GobangGame game});

  bool crateApiGobangGameUndo({required GobangGame game});

  String crateApiApiGreet({required String name});

  Future<void> crateApiApiInitApp();

  RustArcIncrementStrongCountFnType get rust_arc_increment_strong_count_GobangGame;

  RustArcDecrementStrongCountFnType get rust_arc_decrement_strong_count_GobangGame;

  CrossPlatformFinalizerArg get rust_arc_decrement_strong_count_GobangGamePtr;
}

class RustLibApiImpl extends RustLibApiImplPlatform implements RustLibApi {
  RustLibApiImpl({
    required super.handler,
    required super.wire,
    required super.generalizedFrbRustBinding,
    required super.portManager,
  });

  @override
  Future<GobangGame> crateApiGobangGobangGameDefault() {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 1, port: port_);
        },
        codec: SseCodec(
          decodeSuccessData:
              sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame,
          decodeErrorData: null,
        ),
        constMeta: kCrateApiGobangGobangGameDefaultConstMeta,
        argValues: [],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGobangGameDefaultConstMeta =>
      const TaskConstMeta(debugName: "GobangGame_default", argNames: []);

  @override
  Future<int> crateApiGobangGobangGameGetBoardSize({required GobangGame that}) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            that,
            serializer,
          );
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 2, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_i_32, decodeErrorData: null),
        constMeta: kCrateApiGobangGobangGameGetBoardSizeConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGobangGameGetBoardSizeConstMeta =>
      const TaskConstMeta(debugName: "GobangGame_get_board_size", argNames: ["that"]);

  @override
  Future<PieceType> crateApiGobangGobangGameGetCurrentPlayer({required GobangGame that}) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            that,
            serializer,
          );
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 3, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_piece_type, decodeErrorData: null),
        constMeta: kCrateApiGobangGobangGameGetCurrentPlayerConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGobangGameGetCurrentPlayerConstMeta =>
      const TaskConstMeta(debugName: "GobangGame_get_current_player", argNames: ["that"]);

  @override
  Future<GameMode> crateApiGobangGobangGameGetGameMode({required GobangGame that}) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            that,
            serializer,
          );
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 4, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_game_mode, decodeErrorData: null),
        constMeta: kCrateApiGobangGobangGameGetGameModeConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGobangGameGetGameModeConstMeta =>
      const TaskConstMeta(debugName: "GobangGame_get_game_mode", argNames: ["that"]);

  @override
  Future<GameState> crateApiGobangGobangGameGetGameState({required GobangGame that}) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            that,
            serializer,
          );
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 5, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_game_state, decodeErrorData: null),
        constMeta: kCrateApiGobangGobangGameGetGameStateConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGobangGameGetGameStateConstMeta =>
      const TaskConstMeta(debugName: "GobangGame_get_game_state", argNames: ["that"]);

  @override
  Future<int> crateApiGobangGobangGameGetMoveCount({required GobangGame that}) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            that,
            serializer,
          );
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 6, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_i_32, decodeErrorData: null),
        constMeta: kCrateApiGobangGobangGameGetMoveCountConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGobangGameGetMoveCountConstMeta =>
      const TaskConstMeta(debugName: "GobangGame_get_move_count", argNames: ["that"]);

  @override
  Future<PieceType> crateApiGobangGobangGameGetPiece({required GobangGame that, required int row, required int col}) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            that,
            serializer,
          );
          sse_encode_i_32(row, serializer);
          sse_encode_i_32(col, serializer);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 7, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_piece_type, decodeErrorData: null),
        constMeta: kCrateApiGobangGobangGameGetPieceConstMeta,
        argValues: [that, row, col],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGobangGameGetPieceConstMeta =>
      const TaskConstMeta(debugName: "GobangGame_get_piece", argNames: ["that", "row", "col"]);

  @override
  Future<bool> crateApiGobangGobangGameMakeMove({required GobangGame that, required int row, required int col}) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            that,
            serializer,
          );
          sse_encode_i_32(row, serializer);
          sse_encode_i_32(col, serializer);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 8, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_bool, decodeErrorData: null),
        constMeta: kCrateApiGobangGobangGameMakeMoveConstMeta,
        argValues: [that, row, col],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGobangGameMakeMoveConstMeta =>
      const TaskConstMeta(debugName: "GobangGame_make_move", argNames: ["that", "row", "col"]);

  @override
  Future<GobangGame> crateApiGobangGobangGameNew({required BigInt boardSize, required GameMode gameMode}) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_usize(boardSize, serializer);
          sse_encode_game_mode(gameMode, serializer);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 9, port: port_);
        },
        codec: SseCodec(
          decodeSuccessData:
              sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame,
          decodeErrorData: null,
        ),
        constMeta: kCrateApiGobangGobangGameNewConstMeta,
        argValues: [boardSize, gameMode],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGobangGameNewConstMeta =>
      const TaskConstMeta(debugName: "GobangGame_new", argNames: ["boardSize", "gameMode"]);

  @override
  Future<void> crateApiGobangGobangGameReset({required GobangGame that}) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            that,
            serializer,
          );
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 10, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_unit, decodeErrorData: null),
        constMeta: kCrateApiGobangGobangGameResetConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGobangGameResetConstMeta =>
      const TaskConstMeta(debugName: "GobangGame_reset", argNames: ["that"]);

  @override
  Future<bool> crateApiGobangGobangGameUndoMove({required GobangGame that}) {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            that,
            serializer,
          );
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 11, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_bool, decodeErrorData: null),
        constMeta: kCrateApiGobangGobangGameUndoMoveConstMeta,
        argValues: [that],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGobangGameUndoMoveConstMeta =>
      const TaskConstMeta(debugName: "GobangGame_undo_move", argNames: ["that"]);

  @override
  GobangGame crateApiGobangCreateGobangGame() {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 12)!;
        },
        codec: SseCodec(
          decodeSuccessData:
              sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame,
          decodeErrorData: null,
        ),
        constMeta: kCrateApiGobangCreateGobangGameConstMeta,
        argValues: [],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangCreateGobangGameConstMeta =>
      const TaskConstMeta(debugName: "create_gobang_game", argNames: []);

  @override
  int crateApiGobangGameGetBoardSize({required GobangGame game}) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            game,
            serializer,
          );
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 13)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_i_32, decodeErrorData: null),
        constMeta: kCrateApiGobangGameGetBoardSizeConstMeta,
        argValues: [game],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGameGetBoardSizeConstMeta =>
      const TaskConstMeta(debugName: "game_get_board_size", argNames: ["game"]);

  @override
  PieceType crateApiGobangGameGetCurrentPlayer({required GobangGame game}) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            game,
            serializer,
          );
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 14)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_piece_type, decodeErrorData: null),
        constMeta: kCrateApiGobangGameGetCurrentPlayerConstMeta,
        argValues: [game],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGameGetCurrentPlayerConstMeta =>
      const TaskConstMeta(debugName: "game_get_current_player", argNames: ["game"]);

  @override
  int crateApiGobangGameGetMoveCount({required GobangGame game}) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            game,
            serializer,
          );
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 15)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_i_32, decodeErrorData: null),
        constMeta: kCrateApiGobangGameGetMoveCountConstMeta,
        argValues: [game],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGameGetMoveCountConstMeta =>
      const TaskConstMeta(debugName: "game_get_move_count", argNames: ["game"]);

  @override
  PieceType crateApiGobangGameGetPiece({required GobangGame game, required int row, required int col}) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            game,
            serializer,
          );
          sse_encode_i_32(row, serializer);
          sse_encode_i_32(col, serializer);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 16)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_piece_type, decodeErrorData: null),
        constMeta: kCrateApiGobangGameGetPieceConstMeta,
        argValues: [game, row, col],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGameGetPieceConstMeta =>
      const TaskConstMeta(debugName: "game_get_piece", argNames: ["game", "row", "col"]);

  @override
  GameState crateApiGobangGameGetState({required GobangGame game}) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            game,
            serializer,
          );
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 17)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_game_state, decodeErrorData: null),
        constMeta: kCrateApiGobangGameGetStateConstMeta,
        argValues: [game],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGameGetStateConstMeta =>
      const TaskConstMeta(debugName: "game_get_state", argNames: ["game"]);

  @override
  bool crateApiGobangGameMakeMove({required GobangGame game, required int row, required int col}) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            game,
            serializer,
          );
          sse_encode_i_32(row, serializer);
          sse_encode_i_32(col, serializer);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 18)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_bool, decodeErrorData: null),
        constMeta: kCrateApiGobangGameMakeMoveConstMeta,
        argValues: [game, row, col],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGameMakeMoveConstMeta =>
      const TaskConstMeta(debugName: "game_make_move", argNames: ["game", "row", "col"]);

  @override
  void crateApiGobangGameReset({required GobangGame game}) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            game,
            serializer,
          );
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 19)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_unit, decodeErrorData: null),
        constMeta: kCrateApiGobangGameResetConstMeta,
        argValues: [game],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGameResetConstMeta =>
      const TaskConstMeta(debugName: "game_reset", argNames: ["game"]);

  @override
  bool crateApiGobangGameUndo({required GobangGame game}) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
            game,
            serializer,
          );
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 20)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_bool, decodeErrorData: null),
        constMeta: kCrateApiGobangGameUndoConstMeta,
        argValues: [game],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiGobangGameUndoConstMeta => const TaskConstMeta(debugName: "game_undo", argNames: ["game"]);

  @override
  String crateApiApiGreet({required String name}) {
    return handler.executeSync(
      SyncTask(
        callFfi: () {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          sse_encode_String(name, serializer);
          return pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 21)!;
        },
        codec: SseCodec(decodeSuccessData: sse_decode_String, decodeErrorData: null),
        constMeta: kCrateApiApiGreetConstMeta,
        argValues: [name],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiApiGreetConstMeta => const TaskConstMeta(debugName: "greet", argNames: ["name"]);

  @override
  Future<void> crateApiApiInitApp() {
    return handler.executeNormal(
      NormalTask(
        callFfi: (port_) {
          final serializer = SseSerializer(generalizedFrbRustBinding);
          pdeCallFfi(generalizedFrbRustBinding, serializer, funcId: 22, port: port_);
        },
        codec: SseCodec(decodeSuccessData: sse_decode_unit, decodeErrorData: null),
        constMeta: kCrateApiApiInitAppConstMeta,
        argValues: [],
        apiImpl: this,
      ),
    );
  }

  TaskConstMeta get kCrateApiApiInitAppConstMeta => const TaskConstMeta(debugName: "init_app", argNames: []);

  RustArcIncrementStrongCountFnType get rust_arc_increment_strong_count_GobangGame =>
      wire.rust_arc_increment_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame;

  RustArcDecrementStrongCountFnType get rust_arc_decrement_strong_count_GobangGame =>
      wire.rust_arc_decrement_strong_count_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame;

  @protected
  GobangGame dco_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
    dynamic raw,
  ) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return GobangGameImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  GobangGame dco_decode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
    dynamic raw,
  ) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return GobangGameImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  GobangGame dco_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return GobangGameImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  GobangGame dco_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return GobangGameImpl.frbInternalDcoDecode(raw as List<dynamic>);
  }

  @protected
  String dco_decode_String(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as String;
  }

  @protected
  bool dco_decode_bool(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as bool;
  }

  @protected
  GameMode dco_decode_game_mode(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return GameMode.values[raw as int];
  }

  @protected
  GameState dco_decode_game_state(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return GameState.values[raw as int];
  }

  @protected
  int dco_decode_i_32(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as int;
  }

  @protected
  Uint8List dco_decode_list_prim_u_8_strict(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as Uint8List;
  }

  @protected
  PieceType dco_decode_piece_type(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return PieceType.values[raw as int];
  }

  @protected
  int dco_decode_u_8(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return raw as int;
  }

  @protected
  void dco_decode_unit(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return;
  }

  @protected
  BigInt dco_decode_usize(dynamic raw) {
    // Codec=Dco (DartCObject based), see doc to use other codecs
    return dcoDecodeU64(raw);
  }

  @protected
  GobangGame sse_decode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
    SseDeserializer deserializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return GobangGameImpl.frbInternalSseDecode(sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  GobangGame sse_decode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
    SseDeserializer deserializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return GobangGameImpl.frbInternalSseDecode(sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  GobangGame sse_decode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
    SseDeserializer deserializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return GobangGameImpl.frbInternalSseDecode(sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  GobangGame sse_decode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
    SseDeserializer deserializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return GobangGameImpl.frbInternalSseDecode(sse_decode_usize(deserializer), sse_decode_i_32(deserializer));
  }

  @protected
  String sse_decode_String(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_list_prim_u_8_strict(deserializer);
    return utf8.decoder.convert(inner);
  }

  @protected
  bool sse_decode_bool(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getUint8() != 0;
  }

  @protected
  GameMode sse_decode_game_mode(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_i_32(deserializer);
    return GameMode.values[inner];
  }

  @protected
  GameState sse_decode_game_state(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_i_32(deserializer);
    return GameState.values[inner];
  }

  @protected
  int sse_decode_i_32(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getInt32();
  }

  @protected
  Uint8List sse_decode_list_prim_u_8_strict(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var len_ = sse_decode_i_32(deserializer);
    return deserializer.buffer.getUint8List(len_);
  }

  @protected
  PieceType sse_decode_piece_type(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    var inner = sse_decode_i_32(deserializer);
    return PieceType.values[inner];
  }

  @protected
  int sse_decode_u_8(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getUint8();
  }

  @protected
  void sse_decode_unit(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  BigInt sse_decode_usize(SseDeserializer deserializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    return deserializer.buffer.getBigUint64();
  }

  @protected
  void sse_encode_Auto_Owned_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
    GobangGame self,
    SseSerializer serializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize((self as GobangGameImpl).frbInternalSseEncode(move: true), serializer);
  }

  @protected
  void sse_encode_Auto_RefMut_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
    GobangGame self,
    SseSerializer serializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize((self as GobangGameImpl).frbInternalSseEncode(move: false), serializer);
  }

  @protected
  void sse_encode_Auto_Ref_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
    GobangGame self,
    SseSerializer serializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize((self as GobangGameImpl).frbInternalSseEncode(move: false), serializer);
  }

  @protected
  void sse_encode_RustOpaque_flutter_rust_bridgefor_generatedRustAutoOpaqueInnerGobangGame(
    GobangGame self,
    SseSerializer serializer,
  ) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_usize((self as GobangGameImpl).frbInternalSseEncode(move: null), serializer);
  }

  @protected
  void sse_encode_String(String self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_list_prim_u_8_strict(utf8.encoder.convert(self), serializer);
  }

  @protected
  void sse_encode_bool(bool self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putUint8(self ? 1 : 0);
  }

  @protected
  void sse_encode_game_mode(GameMode self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.index, serializer);
  }

  @protected
  void sse_encode_game_state(GameState self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.index, serializer);
  }

  @protected
  void sse_encode_i_32(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putInt32(self);
  }

  @protected
  void sse_encode_list_prim_u_8_strict(Uint8List self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.length, serializer);
    serializer.buffer.putUint8List(self);
  }

  @protected
  void sse_encode_piece_type(PieceType self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    sse_encode_i_32(self.index, serializer);
  }

  @protected
  void sse_encode_u_8(int self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putUint8(self);
  }

  @protected
  void sse_encode_unit(void self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
  }

  @protected
  void sse_encode_usize(BigInt self, SseSerializer serializer) {
    // Codec=Sse (Serialization based), see doc to use other codecs
    serializer.buffer.putBigUint64(self);
  }
}

@sealed
class GobangGameImpl extends RustOpaque implements GobangGame {
  // Not to be used by end users
  GobangGameImpl.frbInternalDcoDecode(List<dynamic> wire) : super.frbInternalDcoDecode(wire, _kStaticData);

  // Not to be used by end users
  GobangGameImpl.frbInternalSseDecode(BigInt ptr, int externalSizeOnNative)
    : super.frbInternalSseDecode(ptr, externalSizeOnNative, _kStaticData);

  static final _kStaticData = RustArcStaticData(
    rustArcIncrementStrongCount: RustLib.instance.api.rust_arc_increment_strong_count_GobangGame,
    rustArcDecrementStrongCount: RustLib.instance.api.rust_arc_decrement_strong_count_GobangGame,
    rustArcDecrementStrongCountPtr: RustLib.instance.api.rust_arc_decrement_strong_count_GobangGamePtr,
  );

  /// 获取棋盘大小
  Future<int> getBoardSize() => RustLib.instance.api.crateApiGobangGobangGameGetBoardSize(that: this);

  /// 获取当前玩家
  Future<PieceType> getCurrentPlayer() => RustLib.instance.api.crateApiGobangGobangGameGetCurrentPlayer(that: this);

  /// 获取游戏模式
  Future<GameMode> getGameMode() => RustLib.instance.api.crateApiGobangGobangGameGetGameMode(that: this);

  /// 获取游戏状态
  Future<GameState> getGameState() => RustLib.instance.api.crateApiGobangGobangGameGetGameState(that: this);

  /// 获取移动历史数量
  Future<int> getMoveCount() => RustLib.instance.api.crateApiGobangGobangGameGetMoveCount(that: this);

  /// 获取指定位置的棋子
  Future<PieceType> getPiece({required int row, required int col}) =>
      RustLib.instance.api.crateApiGobangGobangGameGetPiece(that: this, row: row, col: col);

  /// 尝试落子
  Future<bool> makeMove({required int row, required int col}) =>
      RustLib.instance.api.crateApiGobangGobangGameMakeMove(that: this, row: row, col: col);

  /// 重置游戏
  Future<void> reset() => RustLib.instance.api.crateApiGobangGobangGameReset(that: this);

  /// 悔棋
  Future<bool> undoMove() => RustLib.instance.api.crateApiGobangGobangGameUndoMove(that: this);
}
