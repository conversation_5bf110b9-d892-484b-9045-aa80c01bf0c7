import 'package:flutter/material.dart';
import 'package:xly/xly.dart';

import '../controllers/gobang_controller.dart';

/// 游戏信息面板
class GameInfoPanel extends StatelessWidget {
  const GameInfoPanel({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<GobangController>();

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5DC), // 米色背景
        borderRadius: BorderRadius.circular(8.r),
        boxShadow: [BoxShadow(color: Colors.black.withValues(alpha: 0.1), blurRadius: 4.r, offset: Offset(1.w, 1.h))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 游戏标题
          Text(
            '五子棋',
            style: TextStyle(fontSize: 24.sp, fontWeight: FontWeight.bold, color: const Color(0xFF8B4513)),
          ),
          SizedBox(height: 16.h),

          // 游戏状态
          _buildGameStatus(controller),
          SizedBox(height: 16.h),

          // 当前玩家指示器
          _buildCurrentPlayerIndicator(controller),
          SizedBox(height: 16.h),

          // 游戏统计
          _buildGameStats(controller),
          SizedBox(height: 24.h),

          // 游戏控制按钮
          _buildGameControls(controller),
          SizedBox(height: 24.h),

          // 游戏设置
          _buildGameSettings(controller),
        ],
      ),
    );
  }

  /// 构建游戏状态显示
  Widget _buildGameStatus(GobangController controller) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(color: const Color(0xFFD2B48C)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '游戏状态',
            style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.bold, color: const Color(0xFF8B4513)),
          ),
          SizedBox(height: 8.h),
          Obx(
            () => Text(
              controller.gameStateText,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: controller.isGameOver.value ? Colors.red : const Color(0xFF2E7D32),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建当前玩家指示器
  Widget _buildCurrentPlayerIndicator(GobangController controller) {
    return Obx(
      () => Container(
        padding: EdgeInsets.all(12.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(6.r),
          border: Border.all(color: const Color(0xFFD2B48C)),
        ),
        child: Row(
          children: [
            Text(
              '当前玩家：',
              style: TextStyle(fontSize: 14.sp, color: const Color(0xFF8B4513)),
            ),
            SizedBox(width: 8.w),
            Container(
              width: 20.w,
              height: 20.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: controller.currentPlayerIndex.value == 1
                    ? const RadialGradient(colors: [Color(0xFF404040), Color(0xFF000000)], stops: [0.3, 1.0])
                    : const RadialGradient(colors: [Color(0xFFFFFFFF), Color(0xFFE0E0E0)], stops: [0.3, 1.0]),
                boxShadow: [
                  BoxShadow(color: Colors.black.withValues(alpha: 0.3), blurRadius: 2.r, offset: Offset(1.w, 1.h)),
                ],
              ),
            ),
            SizedBox(width: 8.w),
            Text(
              controller.currentPlayerName,
              style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w600, color: const Color(0xFF8B4513)),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建游戏统计
  Widget _buildGameStats(GobangController controller) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.r),
        border: Border.all(color: const Color(0xFFD2B48C)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '游戏统计',
            style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.bold, color: const Color(0xFF8B4513)),
          ),
          SizedBox(height: 8.h),
          Obx(
            () => Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '步数：',
                  style: TextStyle(fontSize: 12.sp, color: const Color(0xFF8B4513)),
                ),
                Text(
                  '${controller.moveCount.value}',
                  style: TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w600, color: const Color(0xFF8B4513)),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建游戏控制按钮
  Widget _buildGameControls(GobangController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '游戏控制',
          style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.bold, color: const Color(0xFF8B4513)),
        ),
        SizedBox(height: 8.h),
        Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: () async => await controller.resetGame(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF8B4513),
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 8.h),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6.r)),
                ),
                child: Text('重新开始', style: TextStyle(fontSize: 12.sp)),
              ),
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Obx(
                () => ElevatedButton(
                  onPressed: controller.moveCount.value > 0 ? () async => await controller.undoMove() : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF8B4513),
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(vertical: 8.h),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(6.r)),
                  ),
                  child: Text('悔棋', style: TextStyle(fontSize: 12.sp)),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建游戏设置
  Widget _buildGameSettings(GobangController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '游戏设置',
          style: TextStyle(fontSize: 14.sp, fontWeight: FontWeight.bold, color: const Color(0xFF8B4513)),
        ),
        SizedBox(height: 8.h),
        _buildSettingItem('显示坐标', controller.showCoordinates, controller.toggleCoordinates),
        _buildSettingItem('音效', controller.enableSound, controller.toggleSound),
      ],
    );
  }

  /// 构建设置项
  Widget _buildSettingItem(String title, RxBool value, VoidCallback onTap) {
    return Obx(
      () => InkWell(
        onTap: onTap,
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 4.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: TextStyle(fontSize: 12.sp, color: const Color(0xFF8B4513)),
              ),
              Switch(
                value: value.value,
                onChanged: (_) => onTap(),
                activeColor: const Color(0xFF8B4513),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
